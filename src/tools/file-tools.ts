/**
 * Consolidated File Operation Tools
 * Merges functionality from src/tools/core/fileOperations.ts
 */

import { z } from "zod";
import { XcodeServer } from "../server.js";
import { FileService } from "../services/file-service.js";
import { PathService } from "../services/path-service.js";

/**
 * Register all file operation tools (13 tools)
 */
export function registerFileTools(server: XcodeServer) {
  const fileService = new FileService();
  const pathService = new PathService();

  // 1. read_file
  server.server.tool(
    "read_file",
    "Reads the contents of a file within the active project or allowed directories.",
    {
      filePath: z
        .string()
        .describe(
          "Path to the file to read. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      asBinary: z
        .boolean()
        .optional()
        .describe(
          "If true, read the file as binary and return base64-encoded content. Useful for images and other binary files."
        ),
      encoding: z
        .string()
        .optional()
        .describe(
          "Encoding to use when reading the file (e.g., 'utf-8', 'latin1'). Default is 'utf-8'."
        ),
    },
    async ({ filePath, asBinary = false, encoding = "utf-8" }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(filePath);
        server.pathManager.validatePathForReading(resolvedPath);

        const content = await fileService.readFile(resolvedPath, {
          asBinary,
          encoding: encoding as BufferEncoding,
        });

        if (asBinary && Buffer.isBuffer(content)) {
          return {
            content: [
              {
                type: "text" as const,
                text: `Binary file content (base64):\n${content.toString(
                  "base64"
                )}`,
              },
            ],
          };
        }

        return {
          content: [
            {
              type: "text" as const,
              text: String(content),
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to read file: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 2. write_file
  server.server.tool(
    "write_file",
    "Writes or updates the content of a file within the active project or allowed directories.",
    {
      path: z
        .string()
        .describe(
          "Path to the file to update or create. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      content: z.string().describe("The content to be written to the file."),
      createIfMissing: z
        .union([z.boolean(), z.string()])
        .optional()
        .describe("If true, creates the file if it doesn't exist."),
      createPath: z
        .boolean()
        .optional()
        .describe("If true, creates the directory path if it doesn't exist."),
      encoding: z
        .string()
        .optional()
        .describe(
          "Encoding to use when writing the file (e.g., 'utf-8', 'latin1'). Default is 'utf-8'."
        ),
      fromBase64: z
        .boolean()
        .optional()
        .describe(
          "If true, decode the content from base64 before writing. Useful for binary files."
        ),
    },
    async ({
      path: filePath,
      content,
      createPath = true,
      encoding = "utf-8",
      fromBase64 = false,
    }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(filePath);
        server.pathManager.validatePathForWriting(resolvedPath);

        let finalContent: string | Buffer = content;
        if (fromBase64) {
          finalContent = Buffer.from(content, "base64");
        }

        await fileService.writeFile(resolvedPath, finalContent, {
          encoding: encoding as BufferEncoding,
          createPath,
          overwrite: true,
        });

        return {
          content: [
            {
              type: "text" as const,
              text: `File written successfully: ${resolvedPath}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to write file: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 3. copy_file
  server.server.tool(
    "copy_file",
    "Copies a file or directory to a new location within allowed directories.",
    {
      source: z
        .string()
        .describe(
          "Source path. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      destination: z
        .string()
        .describe(
          "Destination path. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      recursive: z
        .boolean()
        .optional()
        .describe("If true, copy directories recursively"),
    },
    async ({ source, destination }) => {
      try {
        const resolvedSource = server.directoryState.resolvePath(source);
        const resolvedDestination =
          server.directoryState.resolvePath(destination);

        server.pathManager.validatePathForReading(resolvedSource);
        server.pathManager.validatePathForWriting(resolvedDestination);

        await fileService.copyFile(resolvedSource, resolvedDestination, {
          overwrite: false,
          preserveTimestamps: true,
        });

        return {
          content: [
            {
              type: "text" as const,
              text: `Successfully copied ${resolvedSource} to ${resolvedDestination}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to copy file: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 4. move_file
  server.server.tool(
    "move_file",
    "Moves a file or directory to a new location within allowed directories.",
    {
      source: z
        .string()
        .describe(
          "Source path. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      destination: z
        .string()
        .describe(
          "Destination path. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
    },
    async ({ source, destination }) => {
      try {
        const resolvedSource = server.directoryState.resolvePath(source);
        const resolvedDestination =
          server.directoryState.resolvePath(destination);

        server.pathManager.validatePathForReading(resolvedSource);
        server.pathManager.validatePathForWriting(resolvedDestination);

        await fileService.moveFile(resolvedSource, resolvedDestination);

        return {
          content: [
            {
              type: "text" as const,
              text: `Successfully moved ${resolvedSource} to ${resolvedDestination}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to move file: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 5. delete_file
  server.server.tool(
    "delete_file",
    "Deletes a file or directory within allowed directories.",
    {
      path: z
        .string()
        .describe(
          "Path to delete. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      recursive: z
        .boolean()
        .optional()
        .describe("If true, delete directories recursively"),
    },
    async ({ path: filePath, recursive = false }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(filePath);
        server.pathManager.validatePathForWriting(resolvedPath);

        await fileService.deleteFile(resolvedPath, { recursive });

        return {
          content: [
            {
              type: "text" as const,
              text: `Successfully deleted: ${resolvedPath}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to delete file: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 6. create_directory
  server.server.tool(
    "create_directory",
    "Creates a new directory within allowed directories.",
    {
      path: z
        .string()
        .describe(
          "Path to create. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
    },
    async ({ path: dirPath }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(dirPath);
        server.pathManager.validatePathForWriting(resolvedPath);

        await fileService.createDirectory(resolvedPath);

        return {
          content: [
            {
              type: "text" as const,
              text: `Successfully created directory: ${resolvedPath}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to create directory: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 7. list_directory
  server.server.tool(
    "list_directory",
    "Lists the contents of a directory, showing both files and subdirectories.",
    {
      path: z
        .string()
        .describe(
          "Path to the directory to list. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      format: z
        .enum(["simple", "detailed"])
        .optional()
        .describe(
          "Format of the output: simple (names only) or detailed (with file information)"
        ),
      showHidden: z
        .boolean()
        .optional()
        .describe("If true, include hidden files (starting with .)"),
    },
    async ({ path: dirPath, format = "simple", showHidden = false }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(dirPath);
        server.pathManager.validatePathForReading(resolvedPath);

        const entries = await fileService.listDirectory(resolvedPath, {
          showHidden,
          includeStats: format === "detailed",
        });

        if (format === "simple") {
          const fileList =
            Array.isArray(entries) && typeof entries[0] === "string"
              ? (entries as string[])
              : (entries as any[]).map((e: any) =>
                  typeof e === "string" ? e : e.path || e.name
                );
          return {
            content: [
              {
                type: "text" as const,
                text: `Directory contents (${
                  fileList.length
                } items):\n${fileList.join("\n")}`,
              },
            ],
          };
        } else {
          // Detailed format
          const detailedEntries =
            Array.isArray(entries) && typeof entries[0] === "object"
              ? (entries as any[])
              : [];
          let result = `Directory contents (${detailedEntries.length} items):\n\n`;

          for (const entry of detailedEntries) {
            if (entry && typeof entry === "object") {
              result += `${entry.isDirectory ? "DIR " : "FILE"} ${
                entry.name || "Unknown"
              }\n`;
              result += `  Size: ${entry.size || 0} bytes\n`;
              result += `  Modified: ${
                entry.lastModified?.toISOString() || "Unknown"
              }\n\n`;
            }
          }

          return {
            content: [
              {
                type: "text" as const,
                text: result,
              },
            ],
          };
        }
      } catch (error) {
        throw new Error(
          `Failed to list directory: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 8. get_file_info
  server.server.tool(
    "get_file_info",
    "Gets detailed information about a file or directory.",
    {
      path: z
        .string()
        .describe(
          "Path to the file or directory. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
    },
    async ({ path: filePath }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(filePath);
        server.pathManager.validatePathForReading(resolvedPath);

        const info = await fileService.getFileInfo(resolvedPath);

        let result = `File Information:\n`;
        result += `Name: ${info.name}\n`;
        result += `Path: ${info.path}\n`;
        result += `Type: ${info.isDirectory ? "Directory" : "File"}\n`;
        result += `Size: ${info.size} bytes\n`;
        result += `Last Modified: ${info.lastModified.toISOString()}\n`;
        result += `Permissions: ${info.permissions}\n`;
        if (info.extension) result += `Extension: ${info.extension}\n`;
        if (info.mimeType) result += `MIME Type: ${info.mimeType}\n`;

        return {
          content: [
            {
              type: "text" as const,
              text: result,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to get file info: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 9. find_files
  server.server.tool(
    "find_files",
    "Searches for files matching a pattern in a directory.",
    {
      path: z
        .string()
        .describe(
          "Directory to search in. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      pattern: z
        .string()
        .describe(
          "Glob pattern to match files (e.g., '*.swift' or '**/*.json')"
        ),
      maxDepth: z
        .number()
        .optional()
        .describe("Maximum directory depth to search"),
      showHidden: z
        .boolean()
        .optional()
        .describe("If true, include hidden files in the search"),
    },
    async ({
      path: searchPath,
      pattern,
      maxDepth = 10,
      showHidden = false,
    }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(searchPath);
        server.pathManager.validatePathForReading(resolvedPath);

        const files = await fileService.findFiles(resolvedPath, pattern, {
          includeHidden: showHidden,
          maxDepth,
        });

        if (files.length === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: `No files found matching pattern '${pattern}' in ${resolvedPath}`,
              },
            ],
          };
        }

        return {
          content: [
            {
              type: "text" as const,
              text: `Found ${
                files.length
              } file(s) matching '${pattern}':\n${files.join("\n")}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to find files: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 10. resolve_path
  server.server.tool(
    "resolve_path",
    "Resolves a path, taking into account the active directory and current project.",
    {
      path: z
        .string()
        .describe(
          "Path to resolve. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
    },
    async ({ path: inputPath }) => {
      try {
        const expandedPath = pathService.expandPath(inputPath);
        const resolvedPath = server.directoryState.resolvePath(expandedPath);

        return {
          content: [
            {
              type: "text" as const,
              text: `Resolved path: ${resolvedPath}`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to resolve path: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 11. check_file_exists
  server.server.tool(
    "check_file_exists",
    "Checks if a file or directory exists at the specified path.",
    {
      path: z
        .string()
        .describe(
          "Path to check. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
    },
    async ({ path: filePath }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(filePath);
        server.pathManager.validatePathForReading(resolvedPath);

        const exists = await fileService.exists(resolvedPath);

        return {
          content: [
            {
              type: "text" as const,
              text: `Path ${resolvedPath} ${
                exists ? "exists" : "does not exist"
              }`,
            },
          ],
        };
      } catch (error) {
        // If validation fails, the path doesn't exist in allowed directories
        return {
          content: [
            {
              type: "text" as const,
              text: `Path ${filePath} does not exist or is not accessible`,
            },
          ],
        };
      }
    }
  );

  // 12. search_in_files
  server.server.tool(
    "search_in_files",
    "Searches for text content within files in a directory.",
    {
      directory: z
        .string()
        .describe(
          "Directory to search in. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      pattern: z
        .string()
        .describe("File pattern to match (e.g., '*.swift', '*.{js,ts}')"),
      searchText: z
        .string()
        .describe("Text or regular expression to search for within the files"),
      caseSensitive: z
        .boolean()
        .optional()
        .describe("If true, perform a case-sensitive search"),
      isRegex: z
        .boolean()
        .optional()
        .describe("If true, treat searchText as a regular expression"),
      maxResults: z
        .number()
        .optional()
        .describe("Maximum number of results to return"),
      includeHidden: z
        .boolean()
        .optional()
        .describe("If true, include hidden files in the search"),
    },
    async ({
      directory,
      pattern,
      searchText,
      caseSensitive = false,
      isRegex = false,
      maxResults = 100,
      includeHidden = false,
    }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(directory);
        server.pathManager.validatePathForReading(resolvedPath);

        const results = await fileService.searchInFiles(
          resolvedPath,
          searchText,
          pattern,
          {
            caseSensitive,
            isRegex,
            maxResults,
            includeHidden,
            contextLines: 2,
          }
        );

        if (results.length === 0) {
          return {
            content: [
              {
                type: "text" as const,
                text: `No matches found for '${searchText}' in files matching '${pattern}'`,
              },
            ],
          };
        }

        let output = `Found ${results.length} match(es) for '${searchText}':\n\n`;

        for (const result of results) {
          output += `${result.file}:${result.line}\n`;
          output += `  ${result.content}\n`;
          if (result.context) {
            if (result.context.before.length > 0) {
              output += `  Context before:\n${result.context.before
                .map((line) => `    ${line}`)
                .join("\n")}\n`;
            }
            if (result.context.after.length > 0) {
              output += `  Context after:\n${result.context.after
                .map((line) => `    ${line}`)
                .join("\n")}\n`;
            }
          }
          output += "\n";
        }

        return {
          content: [
            {
              type: "text" as const,
              text: output,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to search in files: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );

  // 13. analyze_file (placeholder - would integrate with Xcode's static analyzer)
  server.server.tool(
    "analyze_file",
    "Analyzes a source file for potential issues using Xcode's static analyzer.",
    {
      filePath: z
        .string()
        .describe(
          "Path to the source file to analyze. Can be absolute, relative to active directory, or use ~ for home directory."
        ),
      scheme: z
        .string()
        .optional()
        .describe(
          "Optional scheme to use. If not provided, will use the first available scheme."
        ),
      sdk: z
        .string()
        .optional()
        .describe(
          "Optional SDK to use for analysis (e.g., 'iphoneos', 'iphonesimulator'). Defaults to automatic selection based on available devices."
        ),
    },
    async ({ filePath }) => {
      try {
        const resolvedPath = server.directoryState.resolvePath(filePath);
        server.pathManager.validatePathForReading(resolvedPath);

        // For now, return a placeholder message
        // In a full implementation, this would use xcodebuild analyze
        return {
          content: [
            {
              type: "text" as const,
              text: `File analysis for ${resolvedPath}:\n\nNote: Static analysis integration with Xcode is not yet implemented.\nThis would typically run 'xcodebuild analyze' on the file.`,
            },
          ],
        };
      } catch (error) {
        throw new Error(
          `Failed to analyze file: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }
  );
}
