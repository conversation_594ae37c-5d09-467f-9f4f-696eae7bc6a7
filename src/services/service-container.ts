/**
 * Enhanced Service Container for Dependency Injection
 * Consolidates and improves the original serviceContainer.ts
 */

import { ServerConfig } from "../types/index.js";
import { ConfigurationError } from "../utils/core/errors.js";

/**
 * Service lifetime management
 */
export enum ServiceLifetime {
  Singleton = "singleton",
  Transient = "transient",
  Scoped = "scoped",
}

/**
 * Service descriptor interface
 */
export interface ServiceDescriptor<T = any> {
  factory: (container: ServiceContainer) => T;
  lifetime: ServiceLifetime;
  dependencies?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
  instance?: T;
}

/**
 * Service registration options
 */
export interface ServiceOptions {
  lifetime?: ServiceLifetime;
  dependencies?: string[];
  tags?: string[];
  metadata?: Record<string, any>;
  healthCheck?: () => Promise<boolean>;
}

/**
 * Health check result
 */
export interface HealthCheckResult {
  service: string;
  healthy: boolean;
  message?: string;
  timestamp: Date;
}

/**
 * Enhanced Service Container with comprehensive dependency injection
 */
export class ServiceContainer {
  private services = new Map<string, ServiceDescriptor>();
  private singletonInstances = new Map<string, any>();
  private scopedInstances = new Map<string, any>();
  private healthChecks = new Map<string, () => Promise<boolean>>();
  private isDisposed = false;
  private resolutionStack: string[] = [];

  /**
   * Register a service with the container
   */
  register<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    options: ServiceOptions = {}
  ): void {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot register services on a disposed container"
      );
    }

    const descriptor: ServiceDescriptor<T> = {
      factory,
      lifetime: options.lifetime ?? ServiceLifetime.Singleton,
      dependencies: options.dependencies ?? [],
      tags: options.tags ?? [],
      metadata: options.metadata ?? {},
    };

    this.services.set(name, descriptor);

    // Register health check if provided
    if (options.healthCheck) {
      this.healthChecks.set(name, options.healthCheck);
    }
  }

  /**
   * Register a singleton service
   */
  registerSingleton<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    options: Omit<ServiceOptions, "lifetime"> = {}
  ): void {
    this.register(name, factory, {
      ...options,
      lifetime: ServiceLifetime.Singleton,
    });
  }

  /**
   * Register a transient service
   */
  registerTransient<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    options: Omit<ServiceOptions, "lifetime"> = {}
  ): void {
    this.register(name, factory, {
      ...options,
      lifetime: ServiceLifetime.Transient,
    });
  }

  /**
   * Register a scoped service
   */
  registerScoped<T>(
    name: string,
    factory: (container: ServiceContainer) => T,
    options: Omit<ServiceOptions, "lifetime"> = {}
  ): void {
    this.register(name, factory, {
      ...options,
      lifetime: ServiceLifetime.Scoped,
    });
  }

  /**
   * Register an instance directly
   */
  registerInstance<T>(name: string, instance: T): void {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot register services on a disposed container"
      );
    }

    this.singletonInstances.set(name, instance);
    this.services.set(name, {
      factory: () => instance,
      lifetime: ServiceLifetime.Singleton,
      instance,
    });
  }

  /**
   * Resolve a service by name
   */
  resolve<T>(name: string): T {
    if (this.isDisposed) {
      throw new ConfigurationError(
        "Cannot resolve services from a disposed container"
      );
    }

    const descriptor = this.services.get(name);
    if (!descriptor) {
      throw new ConfigurationError(`Service '${name}' is not registered`);
    }

    return this.createInstance<T>(name, descriptor);
  }

  /**
   * Try to resolve a service, returning undefined if not found
   */
  tryResolve<T>(name: string): T | undefined {
    try {
      return this.resolve<T>(name);
    } catch {
      return undefined;
    }
  }

  /**
   * Check if a service is registered
   */
  isRegistered(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Get all registered service names
   */
  getRegisteredServices(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Get services by tag
   */
  getServicesByTag(tag: string): string[] {
    const services: string[] = [];
    for (const [name, descriptor] of this.services) {
      if (descriptor.tags?.includes(tag)) {
        services.push(name);
      }
    }
    return services;
  }

  /**
   * Create service instance based on lifetime
   */
  private createInstance<T>(name: string, descriptor: ServiceDescriptor<T>): T {
    // Check for circular dependencies
    if (this.resolutionStack.includes(name)) {
      throw new ConfigurationError(
        `Circular dependency detected: ${this.resolutionStack.join(
          " -> "
        )} -> ${name}`
      );
    }

    this.resolutionStack.push(name);

    try {
      switch (descriptor.lifetime) {
        case ServiceLifetime.Singleton:
          return this.getSingletonInstance(name, descriptor);
        case ServiceLifetime.Scoped:
          return this.getScopedInstance(name, descriptor);
        case ServiceLifetime.Transient:
          return descriptor.factory(this);
        default:
          throw new ConfigurationError(
            `Unknown service lifetime: ${descriptor.lifetime}`
          );
      }
    } finally {
      this.resolutionStack.pop();
    }
  }

  /**
   * Get or create singleton instance
   */
  private getSingletonInstance<T>(
    name: string,
    descriptor: ServiceDescriptor<T>
  ): T {
    if (this.singletonInstances.has(name)) {
      return this.singletonInstances.get(name);
    }

    const instance = descriptor.factory(this);
    this.singletonInstances.set(name, instance);
    return instance;
  }

  /**
   * Get or create scoped instance
   */
  private getScopedInstance<T>(
    name: string,
    descriptor: ServiceDescriptor<T>
  ): T {
    if (this.scopedInstances.has(name)) {
      return this.scopedInstances.get(name);
    }

    const instance = descriptor.factory(this);
    this.scopedInstances.set(name, instance);
    return instance;
  }

  /**
   * Clear scoped instances
   */
  clearScope(): void {
    this.scopedInstances.clear();
  }

  /**
   * Run health checks for all registered services
   */
  async runHealthChecks(): Promise<HealthCheckResult[]> {
    const results: HealthCheckResult[] = [];

    for (const [serviceName, healthCheck] of this.healthChecks) {
      try {
        const healthy = await healthCheck();
        results.push({
          service: serviceName,
          healthy,
          timestamp: new Date(),
        });
      } catch (error) {
        results.push({
          service: serviceName,
          healthy: false,
          message: error instanceof Error ? error.message : String(error),
          timestamp: new Date(),
        });
      }
    }

    return results;
  }

  /**
   * Dispose of the container and all singleton instances
   */
  dispose(): void {
    if (this.isDisposed) return;

    // Dispose singleton instances that have a dispose method
    for (const instance of this.singletonInstances.values()) {
      if (instance && typeof instance.dispose === "function") {
        try {
          instance.dispose();
        } catch (error) {
          console.warn("Error disposing service instance:", error);
        }
      }
    }

    this.services.clear();
    this.singletonInstances.clear();
    this.scopedInstances.clear();
    this.healthChecks.clear();
    this.isDisposed = true;
  }
}

/**
 * Global service container instance
 */
let globalContainer: ServiceContainer | null = null;

/**
 * Initialize the global service container with all core services
 */
export function initializeGlobalContainer(
  config: ServerConfig
): ServiceContainer {
  if (globalContainer) {
    globalContainer.dispose();
  }

  globalContainer = new ServiceContainer();

  // Register configuration
  globalContainer.registerInstance("config", config);

  // Register core services
  registerCoreServices(globalContainer, config);

  return globalContainer;
}

/**
 * Register all core services in the container
 */
function registerCoreServices(
  container: ServiceContainer,
  config: ServerConfig
): void {
  // Import services dynamically to avoid circular dependencies

  // Register PathService
  container.register(
    "pathManager",
    async () => {
      const { PathService } = await import("./path-service.js");
      return new PathService(config);
    },
    { lifetime: ServiceLifetime.Singleton }
  );

  // Register ProjectDirectoryState
  container.register(
    "directoryState",
    async (container) => {
      const { ProjectDirectoryState } = await import(
        "../utils/project/projectDirectoryState.js"
      );
      const pathManager = container.resolve("pathManager") as any;
      return new ProjectDirectoryState(pathManager);
    },
    {
      lifetime: ServiceLifetime.Singleton,
      dependencies: ["pathManager"],
    }
  );

  // Register CommandService
  container.register(
    "commandExecutor",
    async () => {
      const { SecureCommandExecutor } = await import("./command-service.js");
      return SecureCommandExecutor;
    },
    { lifetime: ServiceLifetime.Singleton }
  );

  // Register CacheService
  container.register(
    "projectCache",
    async () => {
      const { CacheService } = await import("./cache-service.js");
      return new CacheService({
        defaultTtl: 600000, // 10 minutes
        maxSize: 100,
      });
    },
    { lifetime: ServiceLifetime.Singleton }
  );
}

/**
 * Get the global service container
 */
export function getGlobalContainer(): ServiceContainer {
  if (!globalContainer) {
    throw new ConfigurationError(
      "Global container not initialized. Call initializeGlobalContainer first."
    );
  }
  return globalContainer;
}

/**
 * Dispose the global container
 */
export function disposeGlobalContainer(): void {
  if (globalContainer) {
    globalContainer.dispose();
    globalContainer = null;
  }
}
