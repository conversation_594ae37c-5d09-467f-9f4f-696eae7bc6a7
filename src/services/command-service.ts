/**
 * Consolidated Command Service
 * Merges functionality from commandExecutor.ts and commandUtilities.ts
 */

import { spawn } from "child_process";
import { CommandExecutionError } from "../utils/core/errors.js";

/**
 * Command execution options
 */
export interface CommandOptions {
  cwd?: string;
  timeout?: number;
  maxBuffer?: number;
  env?: NodeJS.ProcessEnv;
  retries?: number;
  retryDelay?: number;
}

/**
 * Command execution result
 */
export interface CommandResult {
  stdout: string;
  stderr: string;
  exitCode: number;
  command: string;
  args: string[];
  executionTime: number;
}

/**
 * Command execution statistics
 */
export interface CommandStats {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastExecutionTime: Date;
}

/**
 * Secure Command Execution Service
 */
export class CommandService {
  private static readonly ALLOWED_COMMANDS = new Set([
    // Xcode tools
    "xcodebuild",
    "xcrun",
    "simctl",
    "xcode-select",
    "actool",
    "lldb",
    "xctrace",
    "altool",
    "xcarchive",

    // System tools
    "find",
    "ls",
    "cat",
    "mkdir",
    "cp",
    "mv",
    "rm",
    "chmod",
    "which",
    "sw_vers",

    // Package managers
    "pod",
    "swift",
    "npm",
    "yarn",
    "git",
  ]);

  private static readonly DANGEROUS_PATTERNS = [
    /[;&|`$()]/, // Shell metacharacters
    /\.\./, // Directory traversal
    /\/dev\//, // Device files
    /\/proc\//, // Process files
    /sudo/i, // Privilege escalation
    /rm\s+-rf/i, // Dangerous rm commands
  ];

  private static stats: Map<string, CommandStats> = new Map();

  /**
   * Execute a command with comprehensive security and error handling
   */
  static async execute(
    command: string,
    args: string[] = [],
    options: CommandOptions = {}
  ): Promise<CommandResult> {
    const {
      cwd = process.cwd(),
      timeout = 30000,
      maxBuffer = 1024 * 1024 * 10, // 10MB
      env = process.env,
      retries = 0,
      retryDelay = 1000,
    } = options;

    // Security validation
    this.validateCommand(command, args);

    const startTime = Date.now();
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        if (attempt > 0) {
          await this.delay(retryDelay * Math.pow(2, attempt - 1)); // Exponential backoff
        }

        const result = await this.executeOnce(command, args, {
          cwd,
          timeout,
          maxBuffer,
          env,
        });

        // Update statistics
        this.updateStats(command, true, Date.now() - startTime);

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Don't retry on certain types of errors
        if (error instanceof CommandExecutionError) {
          const cmdError = error as CommandExecutionError;
          // Don't retry on permission errors, file not found, etc.
          if (cmdError.exitCode === 127 || cmdError.exitCode === 126) {
            break;
          }
        }

        if (attempt === retries) {
          break;
        }
      }
    }

    // Update statistics for failed execution
    this.updateStats(command, false, Date.now() - startTime);

    throw lastError || new Error("Command execution failed");
  }

  /**
   * Execute a single command attempt
   */
  private static async executeOnce(
    command: string,
    args: string[],
    options: Required<Omit<CommandOptions, "retries" | "retryDelay">>
  ): Promise<CommandResult> {
    const { cwd, timeout, maxBuffer, env } = options;
    const startTime = Date.now();

    return new Promise((resolve, reject) => {
      const child = spawn(command, args, {
        cwd,
        env: { ...env },
        stdio: ["pipe", "pipe", "pipe"],
        shell: false, // Never use shell for security
      });

      let stdout = "";
      let stderr = "";
      let timeoutId: NodeJS.Timeout | null = null;

      // Set up timeout
      if (timeout > 0) {
        timeoutId = setTimeout(() => {
          child.kill("SIGKILL");
          reject(
            new CommandExecutionError(
              command,
              `Command timed out after ${timeout}ms`,
              -1
            )
          );
        }, timeout);
      }

      // Handle stdout
      child.stdout?.on("data", (data: Buffer) => {
        stdout += data.toString();
        if (stdout.length > maxBuffer) {
          child.kill("SIGKILL");
          reject(
            new CommandExecutionError(
              command,
              "Output buffer exceeded maximum size",
              -1
            )
          );
        }
      });

      // Handle stderr
      child.stderr?.on("data", (data: Buffer) => {
        stderr += data.toString();
        if (stderr.length > maxBuffer) {
          child.kill("SIGKILL");
          reject(
            new CommandExecutionError(
              command,
              "Error buffer exceeded maximum size",
              -1
            )
          );
        }
      });

      // Handle process exit
      child.on("close", (code: number | null) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        const exitCode = code ?? -1;
        const executionTime = Date.now() - startTime;

        const result: CommandResult = {
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode,
          command,
          args,
          executionTime,
        };

        if (exitCode === 0) {
          resolve(result);
        } else {
          reject(
            new CommandExecutionError(
              command,
              stderr || `Command failed with exit code ${exitCode}`,
              exitCode
            )
          );
        }
      });

      // Handle spawn errors
      child.on("error", (error: Error) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        reject(
          new CommandExecutionError(
            command,
            `Failed to spawn process: ${error.message}`,
            -1
          )
        );
      });
    });
  }

  /**
   * Execute a command string (less secure, use sparingly)
   */
  static async executeString(
    commandString: string,
    options: CommandOptions = {}
  ): Promise<CommandResult> {
    // Parse command string into command and arguments
    const parts = this.parseCommandString(commandString);
    if (parts.length === 0) {
      throw new CommandExecutionError(commandString, "Empty command string");
    }

    const [command, ...args] = parts;
    return this.execute(command, args, options);
  }

  /**
   * Validate command for security
   */
  private static validateCommand(command: string, args: string[]): void {
    // Check if command is allowed
    if (!this.ALLOWED_COMMANDS.has(command)) {
      throw new CommandExecutionError(
        command,
        `Command '${command}' is not in the allowed list`
      );
    }

    // Check for dangerous patterns in command and args
    const allParts = [command, ...args];
    for (const part of allParts) {
      for (const pattern of this.DANGEROUS_PATTERNS) {
        if (pattern.test(part)) {
          throw new CommandExecutionError(
            command,
            `Dangerous pattern detected in command: ${part}`
          );
        }
      }
    }

    // Additional validation for specific commands
    if (command === "rm" && args.some((arg) => arg.includes("-rf"))) {
      throw new CommandExecutionError(
        command,
        "Recursive force removal is not allowed"
      );
    }
  }

  /**
   * Parse command string into parts (basic implementation)
   */
  private static parseCommandString(commandString: string): string[] {
    // Simple parsing - in production, consider using a proper shell parser
    return commandString
      .trim()
      .split(/\s+/)
      .filter((part) => part.length > 0);
  }

  /**
   * Update command execution statistics
   */
  private static updateStats(
    command: string,
    success: boolean,
    executionTime: number
  ): void {
    const existing = this.stats.get(command) || {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      averageExecutionTime: 0,
      lastExecutionTime: new Date(),
    };

    existing.totalExecutions++;
    if (success) {
      existing.successfulExecutions++;
    } else {
      existing.failedExecutions++;
    }

    // Update average execution time
    existing.averageExecutionTime =
      (existing.averageExecutionTime * (existing.totalExecutions - 1) +
        executionTime) /
      existing.totalExecutions;

    existing.lastExecutionTime = new Date();

    this.stats.set(command, existing);
  }

  /**
   * Get command execution statistics
   */
  static getStats(
    command?: string
  ): Map<string, CommandStats> | CommandStats | undefined {
    if (command) {
      return this.stats.get(command);
    }
    return new Map(this.stats);
  }

  /**
   * Clear command statistics
   */
  static clearStats(): void {
    this.stats.clear();
  }

  /**
   * Utility delay function
   */
  private static delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Find files using the find command
   */
  static async findFiles(
    searchPath: string,
    patterns: string | string[],
    options: {
      type?: "f" | "d" | "l";
      maxDepth?: number;
      includeHidden?: boolean;
      timeout?: number;
      caseSensitive?: boolean;
    } = {}
  ): Promise<string[]> {
    const {
      type = "f",
      maxDepth,
      includeHidden = false,
      timeout = 30000,
      caseSensitive = false,
    } = options;

    const patternsArray = Array.isArray(patterns) ? patterns : [patterns];
    const results: string[] = [];

    for (const pattern of patternsArray) {
      const args = [searchPath];

      if (maxDepth !== undefined) {
        args.push("-maxdepth", maxDepth.toString());
      }

      args.push("-type", type);

      if (!includeHidden) {
        args.push("-not", "-path", "*/\\.*");
      }

      // Handle case sensitivity
      if (caseSensitive) {
        args.push("-name", pattern);
      } else {
        args.push("-iname", pattern);
      }

      try {
        const { stdout } = await this.execute("find", args, { timeout });
        const files = stdout.trim().split("\n").filter(Boolean);
        results.push(...files);
      } catch (error) {
        // Continue with other patterns if one fails
        console.warn(`Failed to find files with pattern ${pattern}:`, error);
      }
    }

    return [...new Set(results)]; // Remove duplicates
  }
}

/**
 * Convenience functions for backward compatibility
 */
export async function executeCommand(
  command: string,
  args: string[] = [],
  options: CommandOptions = {}
): Promise<CommandResult> {
  return CommandService.execute(command, args, options);
}

export async function executeCommandString(
  commandString: string,
  options: CommandOptions = {}
): Promise<CommandResult> {
  return CommandService.executeString(commandString, options);
}

// Export the main class as SecureCommandExecutor for backward compatibility
export const SecureCommandExecutor = CommandService;

/**
 * Command execution with retry logic
 */
export async function executeWithRetry<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number;
    delay?: number;
    backoff?: boolean;
    retryCondition?: (error: any) => boolean;
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    delay = 1000,
    backoff = true,
    retryCondition = () => true,
  } = options;

  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;

      if (attempt === maxRetries || !retryCondition(error)) {
        throw error;
      }

      const waitTime = backoff ? delay * Math.pow(2, attempt) : delay;
      await new Promise((resolve) => setTimeout(resolve, waitTime));
    }
  }

  throw lastError;
}

/**
 * Execute multiple commands in parallel with concurrency control
 */
export async function executeParallel<T>(
  operations: Array<() => Promise<T>>,
  options: {
    concurrency?: number;
    failFast?: boolean;
  } = {}
): Promise<Array<T | Error>> {
  const { concurrency = 5, failFast = false } = options;
  const results: Array<T | Error> = [];
  const executing: Promise<void>[] = [];

  for (let i = 0; i < operations.length; i++) {
    const operation = operations[i];

    const promise = operation()
      .then((result) => {
        results[i] = result;
      })
      .catch((error) => {
        if (failFast) {
          throw error;
        }
        results[i] = error;
      });

    executing.push(promise);

    if (executing.length >= concurrency) {
      await Promise.race(executing);
      executing.splice(
        executing.findIndex((p) => p === promise),
        1
      );
    }
  }

  await Promise.all(executing);
  return results;
}
